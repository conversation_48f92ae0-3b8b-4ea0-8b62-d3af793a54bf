import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AssessmentForm from './AssessmentForm';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';
import apiService from '../../services/apiService';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import { transformAssessmentScores, validateAssessmentData } from '../../utils/assessmentTransformers';

/**
 * AssessmentFlow Component
 * 
 * Orchestrates the complete assessment process:
 * 1. VIA Character Strengths (Step 1)
 * 2. RIASEC Holland Codes (Step 2) 
 * 3. Big Five Personality (Step 3)
 * 4. Final submission to API
 */
const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentScores, setAssessmentScores] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [isDebugMode] = useState(import.meta.env.DEV && false); // Set to true for debug mode

  // Assessment configurations
  const assessments = [
    {
      step: 1,
      key: 'via',
      data: viaQuestions,
      title: 'VIA Character Strengths'
    },
    {
      step: 2,
      key: 'riasec', 
      data: riasecQuestions,
      title: 'RIASEC Holland Codes'
    },
    {
      step: 3,
      key: 'bigFive',
      data: bigFiveQuestions,
      title: 'Big Five Personality'
    }
  ];

  const currentAssessment = assessments.find(a => a.step === currentStep);
  const totalSteps = assessments.length;

  /**
   * Transform assessment scores to API format using utility functions
   */
  const transformScoresToApiFormat = () => {
    try {
      const apiData = transformAssessmentScores(assessmentScores);

      // Validate the transformed data
      const validation = validateAssessmentData(apiData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      return apiData;
    } catch (error) {
      console.error('🔍 DEBUG - Score transformation error:', error);
      throw error;
    }
  };

  /**
   * Handle assessment completion for current step
   */
  const handleAssessmentSubmit = (scores) => {
    console.log('🔍 DEBUG - AssessmentFlow handleAssessmentSubmit called');
    console.log('🔍 DEBUG - Current step:', currentStep);
    console.log('🔍 DEBUG - Current assessment key:', currentAssessment.key);
    console.log('🔍 DEBUG - Received scores:', scores);

    // Store scores for current assessment
    setAssessmentScores(prev => ({
      ...prev,
      [currentAssessment.key]: scores
    }));

    console.log('🔍 DEBUG - Updated assessment scores:', {
      ...assessmentScores,
      [currentAssessment.key]: scores
    });
  };

  /**
   * Move to next assessment
   */
  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  /**
   * Move to previous assessment
   */
  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  /**
   * Submit all assessments to API
   */
  const submitToApi = async () => {
    setIsSubmitting(true);
    setError('');

    try {
      console.log('🔍 DEBUG - Submitting to API...');
      console.log('🔍 DEBUG - Assessment scores:', assessmentScores);
      
      const apiData = transformScoresToApiFormat();
      console.log('🔍 DEBUG - Transformed API data:', apiData);
      
      const response = await apiService.submitAssessment(apiData);
      console.log('🔍 DEBUG - API response:', response);

      if (response.success && response.data?.jobId) {
        // Navigate to status page with job ID
        navigate(`/assessment/status/${response.data.jobId}`, {
          state: { fromSubmission: true }
        });
      } else {
        throw new Error(response.message || 'Failed to submit assessment');
      }
    } catch (err) {
      console.error('🔍 DEBUG - API submission error:', err);
      setError(err.response?.data?.message || err.message || 'Failed to submit assessment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-submit when all assessments are complete
  useEffect(() => {
    const { via, riasec, bigFive } = assessmentScores;
    
    if (via && riasec && bigFive && !isSubmitting) {
      console.log('🔍 DEBUG - All assessments complete, submitting to API...');
      submitToApi();
    }
  }, [assessmentScores, isSubmitting]);

  // Show loading screen during submission
  if (isSubmitting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner text="Submitting your assessment..." />
          <p className="mt-4 text-gray-600">Please wait while we process your responses</p>
        </div>
      </div>
    );
  }

  // Show error screen if submission failed
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorMessage
            title="Submission Failed"
            message={error}
            onRetry={() => {
              setError('');
              submitToApi();
            }}
            retryText="Try Again"
          />
        </div>
      </div>
    );
  }

  // Render current assessment
  return (
    <AssessmentForm
      assessmentData={currentAssessment.data}
      onSubmit={handleAssessmentSubmit}
      onNext={handleNext}
      onPrevious={handlePrevious}
      isLastAssessment={currentStep === totalSteps}
      currentStep={currentStep}
      totalSteps={totalSteps}
      isDebugMode={isDebugMode}
    />
  );
};

export default AssessmentFlow;
